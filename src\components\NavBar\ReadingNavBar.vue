<template>
  <div>
    <div
      class="action-area-top"
      :class="{ 'pointer-events-none': showToolbox }"
      @click="onActionAreaClick"
    ></div>
    <div
      class="action-area-bottom"
      :class="{ 'pointer-events-none': showToolbox }"
      @click="onActionAreaClick"
    ></div>
    <el-drawer
      v-model="showNavBar"
      :modal="false"
      :size="65"
      :with-header="false"
      :z-index="2045"
      direction="btt"
      modal-class="bottom-drawer-modal"
      @close="onClose"
    >
      <TopNavBar :show-back-only="true" />
    </el-drawer>
    <el-drawer
      v-model="showNavBar"
      :modal="false"
      :size="65"
      :with-header="false"
      class="top-drawer"
      :z-index="2046"
      direction="ttb"
      modal-class="top-drawer-modal"
      @close="onClose"
    >
      <TopNavBar
        @dropdown-visible-change="onDropdownVisibleChange"
        @reset-tour="handleResetTour"
      />
    </el-drawer>
    <div v-show="showNavBar" class="overlay" @click="onClose"></div>
    <ReadingNavBarTour v-if="isFirstLaunch" @tour-end="handleTourEnd" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useTimeoutFn, useEventListener } from "@vueuse/core";
// @ts-expect-error no declaration file for constant
import { LayerType, RECT_ACTION_TYPE } from "@/constant";
// @ts-expect-error no declaration file for TopNavBar
import TopNavBar from "@/components/NavBar/TopNavBar.vue";
import ReadingNavBarTour from "@/components/NavBar/ReadingNavBarTour.vue";
import {
  getIsEditorFirstLaunch,
  setIsEditorFirstLaunch,
} from "@/lib/ConfigDatabase";

const isFirstLaunch = ref(false);
const showNavBar = ref(true);
const showToolbox = ref(false);

const { start, stop } = useTimeoutFn(() => {
  showNavBar.value = false;
}, 3000);

onMounted(() => init());

async function init() {
  isFirstLaunch.value = await getIsEditorFirstLaunch();
  if (isFirstLaunch.value) {
    showNavBar.value = false;
  } else {
    start();
  }
}

const handleResetTour = () => {
  isFirstLaunch.value = true;
  showNavBar.value = false;
};

const handleTourEnd = () => {
  setIsEditorFirstLaunch(false);
  isFirstLaunch.value = false;
  showNavBar.value = true;
  start();
};

const onClose = () => {
  showNavBar.value = false;
};

const onActionAreaClick = () => {
  // 当 showToolbox 为 true 时，CSS pointer-events: none 会自动透传事件
  // 这个函数只在 showToolbox 为 false 时被调用
  showNavBar.value = true;
};

function onDropdownVisibleChange(visible: boolean) {
  if (visible) {
    stop();
  }
}

useEventListener("message", (event) => {
  const { layerType, type } = event.data;
  if (layerType === LayerType.RECT && type === RECT_ACTION_TYPE.SHOW_TOOLBOX) {
    showToolbox.value = Boolean(event.data.showToolbox ?? false);
  }
});
</script>

<style scoped>
:deep(.top-drawer) {
  position: relative;
}

:deep(.top-drawer-modal) {
  height: 65px;
}

.action-area-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 30px;
}

.action-area-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
}

.overlay {
  position: fixed;
  top: 65px;
  bottom: 65px;
  left: 0;
  width: 100%;
  z-index: 2047;
}

.pointer-events-none {
  pointer-events: none;
}
</style>
